from sqlalchemy import Column, String, DateTime, Integer, Text, ForeignKey
from sqlalchemy.dialects.postgresql import UUI<PERSON>, JSON
from .baseModel import BaseModel
from datetime import datetime, timezone
import uuid

class AdminActionLog(BaseModel):
    __tablename__ = "admin_action_logs"
    admin_id = Column(String, nullable=False)
    action = Column(String, nullable=False)
    session_id = Column(String, nullable=False)
    reason = Column(String, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)

class ReconnectionRequest(BaseModel):
    __tablename__ = "reconnection_requests"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(String, nullable=False)
    student_id = Column(String, nullable=False)
    exam_id = Column(String, nullable=False)
    reason = Column(String, nullable=False)
    status = Column(String, default="pending_approval")  # pending_approval, approved, denied
    requested_at = Column(DateTime, default=datetime.utcnow)
    approved_by = Column(String, nullable=True)
    approved_at = Column(DateTime, nullable=True)
    denied_by = Column(String, nullable=True)
    denied_at = Column(DateTime, nullable=True)
    teacher_reason = Column(String, nullable=True)


class ExamSession(BaseModel):
    """Database model for exam sessions (replaces Redis storage)"""
    __tablename__ = "exam_sessions"

    session_id = Column(String, unique=True, nullable=False, index=True)
    student_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    exam_id = Column(UUID(as_uuid=True), ForeignKey("exams.id"), nullable=False)
    status = Column(String, default="active", nullable=False)  # active, submitted, disqualified, pending_reconnection
    start_time = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    end_time = Column(DateTime(timezone=True), nullable=True)
    duration = Column(Integer, nullable=False)  # Duration in seconds
    strikes = Column(Integer, default=0, nullable=False)
    last_heartbeat = Column(DateTime(timezone=True), nullable=True)
    disqualification_reason = Column(Text, nullable=True)
    session_data = Column(JSON, nullable=True)  # Store additional session metadata

    # Timestamps
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc), nullable=False)