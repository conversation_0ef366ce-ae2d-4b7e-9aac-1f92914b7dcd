"""
Background services module.

This module contains background tasks and services that run continuously.
"""

import asyncio
import json
import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional

from config.session import get_db
from Models.Exam import StudentExamAttempt, StudentExamAnswer
from Models.ExamSession import ExamSession


class BackgroundServiceManager:
    """Manager for background services and tasks."""
    
    def __init__(self):
        self.auto_submit_task: Optional[asyncio.Task] = None
    
    async def start_services(self):
        """Start all background services."""
        self.auto_submit_task = asyncio.create_task(self._auto_submit_expired_sessions())
        print("Background auto-submit task started")
    
    async def stop_services(self):
        """Stop all background services."""
        if self.auto_submit_task:
            self.auto_submit_task.cancel()
            try:
                await self.auto_submit_task
            except asyncio.CancelledError:
                pass
        print("Background tasks cancelled")
    
    async def _auto_submit_expired_sessions(self):
        """
        Background task to automatically submit expired exam sessions.
        
        This task runs continuously and checks for expired exam sessions,
        automatically submitting them when the time limit is reached.
        """
        while True:
            try:
                # Get database session
                db = next(get_db())

                # Find expired active sessions
                current_time = datetime.now(timezone.utc)
                expired_sessions = db.query(ExamSession).filter(
                    ExamSession.status == "active"
                ).all()

                for session in expired_sessions:
                    if session.start_time and session.duration:
                        end_time = session.start_time + timedelta(seconds=session.duration)
                        if current_time >= end_time:
                            # Auto-submit the expired session
                            await self._submit_expired_session(session, db)

                db.close()

            except Exception as e:
                print(f"[AutoSubmit] Error: {e}")

            # Wait 60 seconds before next check
            await asyncio.sleep(60)
    
    async def _submit_expired_session(self, session: ExamSession, db):
        """
        Submit an expired exam session.

        Args:
            session (ExamSession): The exam session from database
            db: Database session
        """
        try:
            answers = session.session_data.get("answers", {}) if session.session_data else {}

            print(f"[AutoSubmit] Creating attempt for exam {session.exam_id}, student {session.student_id}")

            # Create the exam attempt
            attempt = StudentExamAttempt(
                id=uuid.uuid4(),
                exam_id=session.exam_id,
                student_id=session.student_id,
                started_at=session.start_time,
                completed_at=datetime.now(timezone.utc),
                is_teacher_checked=False,
                is_ai_checked=False,
                status="auto_submitted"
            )
            db.add(attempt)
            db.commit()
            db.refresh(attempt)

            # Save answers with comprehensive data
            current_time = datetime.now(timezone.utc)

            for qid, ans in answers.items():
                question_id = uuid.UUID(qid) if isinstance(qid, str) else qid

                # Handle both simple string answers and complex answer objects
                if isinstance(ans, dict):
                    answer_text = ans.get('answer', ans.get('text', ''))
                    time_spent = ans.get('time_spent_seconds', None)
                else:
                    answer_text = str(ans) if ans is not None else ''
                    time_spent = None

                student_answer = StudentExamAnswer(
                    attempt_id=attempt.id,
                    question_id=question_id,
                    answer=answer_text,  # Main answer field
                    answer_text=answer_text,  # Compatibility field
                    submitted_at=current_time,
                    time_spent_seconds=time_spent
                )
                db.add(student_answer)

            # Update session status
            session.status = "auto_submitted"
            session.end_time = datetime.now(timezone.utc)

            db.commit()
            print(f"[AutoSubmit] Successfully auto-submitted exam for student {session.student_id}")

        except Exception as e:
            print(f"[AutoSubmit] Error submitting session: {e}")
            db.rollback()
