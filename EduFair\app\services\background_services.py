"""
Background services module.

This module contains background tasks and services that run continuously.
"""

import asyncio
import json
import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional

from config.redis import get_redis
from config.session import get_db
from Models.Exam import StudentExamAttempt, StudentExamAnswer


class BackgroundServiceManager:
    """Manager for background services and tasks."""
    
    def __init__(self):
        self.auto_submit_task: Optional[asyncio.Task] = None
    
    async def start_services(self):
        """Start all background services."""
        self.auto_submit_task = asyncio.create_task(self._auto_submit_expired_sessions())
        print("Background auto-submit task started")
    
    async def stop_services(self):
        """Stop all background services."""
        if self.auto_submit_task:
            self.auto_submit_task.cancel()
            try:
                await self.auto_submit_task
            except asyncio.CancelledError:
                pass
        print("Background tasks cancelled")
    
    async def _auto_submit_expired_sessions(self):
        """
        Background task to automatically submit expired exam sessions.
        
        This task runs continuously and checks for expired exam sessions,
        automatically submitting them when the time limit is reached.
        """
        while True:
            try:
                redis = await get_redis()
                # List all keys matching exam_session:*
                keys = await redis.keys("exam_session:*")
                
                for key in keys:
                    session = await redis.hgetall(key)
                    if not session or session.get("status") != "active":
                        continue
                    
                    start_time = session.get("start_time")
                    duration = int(session.get("duration", "0"))
                    
                    if not start_time or not duration:
                        continue
                    
                    start_dt = datetime.fromisoformat(start_time)
                    end_dt = start_dt + timedelta(seconds=duration)
                    
                    if datetime.now(timezone.utc) >= end_dt:
                        # Auto-submit the expired session
                        await self._submit_expired_session(session, key)
                        
            except Exception as e:
                print(f"[AutoSubmit] Error: {e}")
            
            # Wait 60 seconds before next check
            await asyncio.sleep(60)
    
    async def _submit_expired_session(self, session: dict, session_key: str):
        """
        Submit an expired exam session.
        
        Args:
            session (dict): The exam session data from Redis
            session_key (str): The Redis key for the session
        """
        db = next(get_db())
        redis = await get_redis()
        
        try:
            answers = json.loads(session.get("answers", "{}"))

            # Ensure UUIDs are properly converted
            exam_id = session["exam_id"]
            student_id = session["student_id"]

            # Convert to UUID if they're strings
            if isinstance(exam_id, str):
                exam_id = uuid.UUID(exam_id)
            if isinstance(student_id, str):
                student_id = uuid.UUID(student_id)

            print(f"[AutoSubmit] Creating attempt for exam {exam_id}, student {student_id}")

            # Create the exam attempt
            start_time = session.get("start_time")
            start_dt = datetime.fromisoformat(start_time)
            
            attempt = StudentExamAttempt(
                id=uuid.uuid4(),
                exam_id=exam_id,
                student_id=student_id,
                started_at=start_dt,
                completed_at=datetime.now(timezone.utc),
                is_teacher_checked=False,
                is_ai_checked=False,
            )
            db.add(attempt)
            db.commit()
            db.refresh(attempt)

            # Save answers with comprehensive data and proper UUID conversion
            current_time = datetime.now(timezone.utc)

            for qid, ans in answers.items():
                question_id = uuid.UUID(qid) if isinstance(qid, str) else qid

                # Handle both simple string answers and complex answer objects
                if isinstance(ans, dict):
                    answer_text = ans.get('answer', ans.get('text', ''))
                    time_spent = ans.get('time_spent_seconds', None)
                else:
                    answer_text = str(ans) if ans is not None else ''
                    time_spent = None

                student_answer = StudentExamAnswer(
                    attempt_id=attempt.id,
                    question_id=question_id,
                    answer=answer_text,  # Main answer field
                    answer_text=answer_text,  # Compatibility field
                    submitted_at=current_time,
                    time_spent_seconds=time_spent
                )
                db.add(student_answer)

            db.commit()
            await redis.delete(session_key)
            print(f"[AutoSubmit] Successfully auto-submitted exam for student {student_id}")

        except Exception as e:
            print(f"[AutoSubmit] Error submitting session: {e}")
            db.rollback()
        finally:
            db.close()
