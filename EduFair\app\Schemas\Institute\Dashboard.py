from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from uuid import UUID
from decimal import Decimal


class DashboardSummaryOut(BaseModel):
    """Institute dashboard summary statistics"""
    total_mentors: int = Field(..., description="Total number of mentors")
    active_mentors: int = Field(..., description="Number of active mentors")
    total_events: int = Field(..., description="Total number of events")
    upcoming_events: int = Field(..., description="Number of upcoming events")
    completed_events: int = Field(..., description="Number of completed events")
    total_event_attendees: int = Field(..., description="Total event attendees across all events")
    monthly_growth: Dict[str, float] = Field(..., description="Monthly growth percentages")


class QuickStatsOut(BaseModel):
    """Quick statistics for institute dashboard"""
    this_month_events: int = Field(..., description="Events this month")
    this_month_new_mentors: int = Field(..., description="New mentors this month")
    this_month_attendees: int = Field(..., description="Event attendees this month")
    average_event_rating: Optional[float] = Field(None, description="Average event rating")
    mentor_satisfaction_score: Optional[float] = Field(None, description="Average mentor satisfaction")
    total_revenue: Optional[Decimal] = Field(None, description="Total revenue from events")


class RecentActivityOut(BaseModel):
    """Recent activity item"""
    id: UUID
    activity_type: str = Field(..., description="Type of activity (mentor_application, event_created, etc.)")
    title: str = Field(..., description="Activity title")
    description: str = Field(..., description="Activity description")
    actor_name: Optional[str] = Field(None, description="Name of the person who performed the activity")
    actor_id: Optional[UUID] = Field(None, description="ID of the person who performed the activity")
    related_entity_id: Optional[UUID] = Field(None, description="ID of related entity (mentor, event, etc.)")
    related_entity_type: Optional[str] = Field(None, description="Type of related entity")
    created_at: datetime = Field(..., description="When the activity occurred")
    is_read: bool = Field(default=False, description="Whether the activity has been read")
    priority: str = Field(default="normal", description="Priority level (low, normal, high, urgent)")


class NotificationOut(BaseModel):
    """Institute notification"""
    id: UUID
    title: str = Field(..., description="Notification title")
    message: str = Field(..., description="Notification message")
    notification_type: str = Field(..., description="Type of notification")
    priority: str = Field(default="normal", description="Priority level")
    is_read: bool = Field(default=False, description="Whether notification has been read")
    action_url: Optional[str] = Field(None, description="URL for notification action")
    action_text: Optional[str] = Field(None, description="Text for action button")
    related_entity_id: Optional[UUID] = Field(None, description="ID of related entity")
    related_entity_type: Optional[str] = Field(None, description="Type of related entity")
    created_at: datetime = Field(..., description="When notification was created")
    expires_at: Optional[datetime] = Field(None, description="When notification expires")


class MentorPerformanceMetric(BaseModel):
    """Mentor performance metric for analytics"""
    month: str = Field(..., description="Month in YYYY-MM format")
    active_mentors: int = Field(..., description="Number of active mentors")
    new_applications: int = Field(..., description="New mentor applications")
    approval_rate: float = Field(..., description="Application approval rate percentage")
    average_rating: Optional[float] = Field(None, description="Average mentor rating")


class EventEngagementMetric(BaseModel):
    """Event engagement metric for analytics"""
    event_id: UUID
    title: str = Field(..., description="Event title")
    attendees: int = Field(..., description="Number of attendees")
    attendance_rate: float = Field(..., description="Attendance rate percentage")
    satisfaction_score: Optional[float] = Field(None, description="Event satisfaction score")
    revenue: Optional[Decimal] = Field(None, description="Revenue generated")
    start_date: datetime = Field(..., description="Event start date")


class MentorEffectivenessMetric(BaseModel):
    """Mentor effectiveness metric"""
    mentor_id: UUID
    name: str = Field(..., description="Mentor name")
    specialization: Optional[str] = Field(None, description="Mentor specialization")
    sessions_completed: int = Field(..., description="Number of sessions completed")
    average_rating: Optional[float] = Field(None, description="Average rating")
    response_time: Optional[str] = Field(None, description="Average response time")
    total_students: int = Field(default=0, description="Total students mentored")


class AnalyticsOut(BaseModel):
    """Comprehensive analytics data"""
    mentor_performance: List[MentorPerformanceMetric] = Field(..., description="Mentor performance over time")
    event_stats: List[EventEngagementMetric] = Field(..., description="Event engagement statistics")
    mentor_effectiveness: List[MentorEffectivenessMetric] = Field(..., description="Individual mentor effectiveness")


class GrowthMetricsOut(BaseModel):
    """Growth metrics for institute"""
    period: str = Field(..., description="Time period (month, quarter, year)")
    mentor_growth: float = Field(..., description="Mentor growth percentage")
    event_growth: float = Field(..., description="Event growth percentage")
    attendee_growth: float = Field(..., description="Attendee growth percentage")
    revenue_growth: Optional[float] = Field(None, description="Revenue growth percentage")


class CustomReportRequest(BaseModel):
    """Request for custom report generation"""
    report_name: str = Field(..., description="Name of the report")
    report_type: str = Field(..., description="Type of report (mentor, event, financial)")
    date_range: Dict[str, datetime] = Field(..., description="Date range for the report")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional filters")
    include_charts: bool = Field(default=True, description="Whether to include charts")
    format: str = Field(default="pdf", description="Report format (pdf, excel, csv)")


class CustomReportOut(BaseModel):
    """Custom report response"""
    report_id: UUID
    report_name: str
    report_type: str
    status: str = Field(..., description="Report generation status")
    download_url: Optional[str] = Field(None, description="URL to download the report")
    created_at: datetime
    expires_at: Optional[datetime] = Field(None, description="When the report expires")


class EventSuccessMetric(BaseModel):
    """Event success metrics"""
    total_events: int
    successful_events: int = Field(..., description="Events with >80% attendance")
    average_attendance_rate: float
    average_satisfaction_score: Optional[float]
    total_revenue: Optional[Decimal]
    roi_percentage: Optional[float] = Field(None, description="Return on investment percentage")


class RecentActivitiesResponse(BaseModel):
    """Response for recent activities"""
    activities: List[RecentActivityOut]
    total_unread: int = Field(..., description="Total number of unread activities")
    has_more: bool = Field(..., description="Whether there are more activities")


class NotificationsResponse(BaseModel):
    """Response for notifications"""
    notifications: List[NotificationOut]
    total_unread: int = Field(..., description="Total number of unread notifications")
    has_more: bool = Field(..., description="Whether there are more notifications")
