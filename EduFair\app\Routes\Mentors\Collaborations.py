"""
Routes for Mentor-Institute Collaborations
Handles both mentor-initiated and institute-initiated invitations and collaborations
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Mentors.CollaborationCrud import (
    create_collaboration, get_collaboration_by_id, update_collaboration,
    delete_collaboration, list_collaborations
)
from Cruds.Institute.Mentor import (
    create_invite_to_institute, create_invite_to_mentor,
    list_sent_invites_to_institutes, list_received_invites_from_institutes,
    respond_to_received_invite, list_sent_invites_to_mentors
)

# Import Schemas
from Schemas.Mentors.MentorInstitutes import (
    MentorInstituteInvite, MentorInstituteInviteOut, InvitationListResponse,
    CollaborationCreate, CollaborationUpdate, CollaborationDetails,
    CollaborationListResponse
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# === COLLABORATION MANAGEMENT ROUTES ===

@router.post("/collaborations", response_model=CollaborationDetails)
def create_new_collaboration(
    collaboration_data: CollaborationCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Create a new collaboration (admin/direct creation)"""
    current_user = get_current_user(token, db)
    return create_collaboration(db, collaboration_data)


@router.get("/collaborations/{collaboration_id}", response_model=CollaborationDetails)
def get_collaboration(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Get collaboration details by ID"""
    current_user = get_current_user(token, db)
    collaboration = get_collaboration_by_id(db, collaboration_id)
    
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    # Check if user has access to this collaboration
    if (current_user.user_type == "mentor" and collaboration.mentor.id != current_user.id) or \
       (current_user.user_type == "institute" and collaboration.institute.id != current_user.id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    return collaboration


@router.put("/collaborations/{collaboration_id}", response_model=CollaborationDetails)
def update_collaboration_details(
    collaboration_id: UUID,
    update_data: CollaborationUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Update collaboration details"""
    current_user = get_current_user(token, db)
    
    # Check access first
    collaboration = get_collaboration_by_id(db, collaboration_id)
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    if (current_user.user_type == "mentor" and collaboration.mentor.id != current_user.id) or \
       (current_user.user_type == "institute" and collaboration.institute.id != current_user.id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    return update_collaboration(db, collaboration_id, update_data)


@router.delete("/collaborations/{collaboration_id}")
def delete_collaboration_endpoint(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Delete/end collaboration"""
    current_user = get_current_user(token, db)
    
    # Check access first
    collaboration = get_collaboration_by_id(db, collaboration_id)
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    if (current_user.user_type == "mentor" and collaboration.mentor.id != current_user.id) or \
       (current_user.user_type == "institute" and collaboration.institute.id != current_user.id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    delete_collaboration(db, collaboration_id)
    return {"message": "Collaboration deleted successfully"}


@router.get("/collaborations", response_model=CollaborationListResponse)
def list_user_collaborations(
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """List collaborations for current user"""
    current_user = get_current_user(token, db)
    
    return list_collaborations(
        db=db,
        user_id=current_user.id,
        user_type=current_user.user_type,
        status_filter=status_filter,
        page=page,
        size=size
    )


# === INVITATION ROUTES ===

@router.post("/invitations/send-to-institute", response_model=MentorInstituteInviteOut)
def send_invitation_to_institute(
    invitation_data: MentorInstituteInvite,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Mentor sends invitation to institute"""
    current_user = get_current_user(token, db)
    return create_invite_to_institute(db, current_user.id, invitation_data)


@router.post("/invitations/send-to-mentor", response_model=MentorInstituteInviteOut)
def send_invitation_to_mentor(
    invitation_data: MentorInstituteInvite,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Institute sends invitation to mentor"""
    current_user = get_current_user(token, db)
    return create_invite_to_mentor(db, current_user.id, invitation_data)


@router.get("/invitations/sent", response_model=InvitationListResponse)
def get_sent_invitations(
    status_filter: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Get invitations sent by current user"""
    current_user = get_current_user(token, db)
    
    if current_user.user_type == "mentor":
        return list_sent_invites_to_institutes(db, current_user.id, page, size, status_filter)
    else:
        return list_sent_invites_to_mentors(db, current_user.id, page, size, status_filter)


@router.get("/invitations/received", response_model=InvitationListResponse)
def get_received_invitations(
    status_filter: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Get invitations received by current user"""
    current_user = get_current_user(token, db)
    return list_received_invites_from_institutes(db, current_user.id, page, size, status_filter)


@router.post("/invitations/{invitation_id}/respond", response_model=CollaborationDetails)
def respond_to_invitation(
    invitation_id: UUID,
    response: dict,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["mentor", "institute"]))
):
    """Respond to received invitation"""
    current_user = get_current_user(token, db)
    return respond_to_received_invite(db, current_user.id, invitation_id, response)
