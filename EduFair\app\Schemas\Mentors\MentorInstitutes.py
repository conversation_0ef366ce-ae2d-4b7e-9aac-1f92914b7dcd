from pydantic import BaseModel, <PERSON>, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum

class InviteReceivedByEnum(str, Enum):
    mentor = "mentor"
    institute = "institute"


# Basic schemas for Mentor and Institute in collaborations
class Mentor(BaseModel):
    id: UUID
    username: str
    email: str
    full_name: Optional[str] = None
    bio: Optional[str] = None
    experience_years: Optional[int] = None
    hourly_rate: Optional[float] = None

    class Config:
        from_attributes = True


class Institute(BaseModel):
    id: UUID
    username: str
    email: str
    institute_name: Optional[str] = None
    description: Optional[str] = None
    website: Optional[str] = None

    class Config:
        from_attributes = True


class MentorInstituteInvite(BaseModel):
    receiver_id: UUID
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    received_by: Optional[InviteReceivedByEnum] = None

    class Config:
        from_attributes = True
# Sender details for invitations
class InvitationSenderDetails(BaseModel):
    id: UUID
    username: str
    email: str
    profile_picture: Optional[str] = None
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data

    # Mentor-specific fields (when sender is mentor)
    mentor_bio: Optional[str] = None
    mentor_experience_years: Optional[int] = None
    mentor_hourly_rate: Optional[float] = None
    mentor_languages: Optional[List[str]] = None

    # Institute-specific fields (when sender is institute)
    institute_name: Optional[str] = None
    institute_description: Optional[str] = None
    institute_website: Optional[str] = None
    institute_city: Optional[str] = None
    institute_country: Optional[str] = None
    institute_is_verified: Optional[bool] = None
    institute_logo: Optional[Dict[str, Any]] = None  # Base64 image data

    class Config:
        from_attributes = True


class MentorInstituteInviteOut(BaseModel):
    id: UUID
    receiver_id: UUID
    mentor_id: Optional[UUID] = None
    institute_id: Optional[UUID] = None
    mentor_email: Optional[str] = None
    invitation_message: Optional[str] = None
    status: Optional[str] = None
    proposed_hourly_rate: Optional[float] = None
    proposed_hours_per_week: Optional[int] = None
    expertise_areas_needed: Optional[List[str]] = None
    contract_terms: Optional[str] = None
    invited_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    responded_at: Optional[datetime] = None
    response_message: Optional[str] = None
    received_by: Optional[InviteReceivedByEnum] = None

    # Legacy fields for backward compatibility
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None

    created_at: datetime
    updated_at: datetime

    # Enhanced sender details
    sender: Optional[InvitationSenderDetails] = None

    class Config:
        from_attributes = True
class InvitationListResponse(BaseModel):
    invitations: List[MentorInstituteInviteOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool

    class Config:
        from_attributes = True
# CRUD Schemas for Collaboration
class CollaborationCreate(BaseModel):
    mentor_id: UUID
    institute_id: UUID
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    contract_terms: Optional[str] = None
    start_date: Optional[datetime] = None

    class Config:
        from_attributes = True


class CollaborationUpdate(BaseModel):
    status: Optional[str] = None
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    contract_terms: Optional[str] = None
    end_date: Optional[datetime] = None

    class Config:
        from_attributes = True


class CollaborationDetails(BaseModel):
    id: UUID
    mentor: Mentor
    institute: Institute
    status: str
    hourly_rate: Optional[float] = None
    hours_per_week: Optional[int] = None
    contract_terms: Optional[str] = None
    start_date: datetime
    end_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CollaborationListResponse(BaseModel):
    collaborations: List[CollaborationDetails]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool

    class Config:
        from_attributes = True