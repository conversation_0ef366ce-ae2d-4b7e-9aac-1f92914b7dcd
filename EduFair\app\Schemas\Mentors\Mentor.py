from pydantic import BaseModel, <PERSON>, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum



class MentorProfileUpdate(BaseModel):
    bio: Optional[str] = Field(None, max_length=2000)
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)

    # Subject relationships (UUIDs)
    expertise_subject_ids: Optional[List[UUID]] = None
    preferred_subject_ids: Optional[List[UUID]] = None

    # Languages and availability
    languages: Optional[List[str]] = None
    availability_hours: Optional[Dict[str, Any]] = None
    profile_image_url: Optional[str] = None




# Output Schemas
class MentorProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    bio: Optional[str]
    experience_years: Optional[int]
    hourly_rate: Optional[Decimal]
    languages: Optional[List[str]]
    availability_hours: Optional[Dict[str, Any]]
    profile_image_url: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata

    # Subject relationships (will be populated from relationships)
    expertise_subjects: Optional[List[Dict[str, Any]]] = None
    preferred_subjects: Optional[List[Dict[str, Any]]] = None

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MentorUserOut(BaseModel):
    id: UUID
    username: str
    email: str
    mobile: str
    country: str
    profile_picture: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata
    user_type: str
    is_email_verified: bool
    is_mobile_verified: bool
    created_at: datetime
    mentor_profile: Optional[MentorProfileOut]

    class Config:
        from_attributes = True


class MentorDetailedOut(BaseModel):
    user: MentorUserOut
    profile: MentorProfileOut
    total_competitions: int = 0
    active_institutes: int = 0
    average_rating: Optional[Decimal] = None
    verification_status: str

    class Config:
        from_attributes = True


class MentorListOut(BaseModel):
    id: UUID
    username: str
    full_name: str
    email: str
    mobile: str
    country: Optional[str]
    bio: Optional[str]
    expertise_areas: Optional[List[str]]
    experience_years: Optional[int]
    current_position: Optional[str]
    hourly_rate: Optional[Decimal]
    languages: Optional[List[str]]
    rating: Optional[Decimal]
    profile_image_url: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata
    created_at: datetime

    class Config:
        from_attributes = True


class MentorListResponse(BaseModel):
    mentors: List[MentorListOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool
