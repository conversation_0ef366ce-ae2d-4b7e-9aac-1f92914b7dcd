"""
Static files configuration module.

This module handles static file mounting and upload directory setup.
"""

import os
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from config.config import settings


def setup_static_files(app: FastAPI):
    """
    Configure static file serving for the FastAPI application.
    
    Args:
        app (FastAPI): The FastAPI application instance
    """
    # Create upload directory if it doesn't exist
    upload_dir = os.path.join(os.getcwd(), settings.UPLOAD_DIR)
    os.makedirs(upload_dir, exist_ok=True)

    # Mount static files for serving uploaded files
    app.mount(
        settings.STATIC_FILES_URL,
        StaticFiles(directory=upload_dir),
        name="static"
    )
