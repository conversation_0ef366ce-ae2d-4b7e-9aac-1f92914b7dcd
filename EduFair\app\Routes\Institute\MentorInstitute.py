from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from uuid import UUID
from typing import Optional

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

# Import CRUD functions
from Cruds.Institute.Mentor import (
    create_invite_to_mentor, list_sent_invites_to_mentors,
    list_received_invites_from_institutes, respond_to_received_invite_as_institute
)
from Schemas.Mentors.MentorInstitutes import (
    MentorInstituteInvite, MentorInstituteInviteOut, InvitationListResponse,
    InviteReceivedByEnum, CollaborationDetails
)

router = APIRouter()

# =========================================================
#                      INVITE ROUTES
# =========================================================

@router.post("/invite", response_model=MentorInstituteInviteOut)
def send_invite(
    invite_data: MentorInstituteInvite,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute","mentor"]))
):
    current_user = get_current_user(token, db)
    invite = create_invite_to_mentor(db, current_user.id, invite_data)
    return invite


@router.get("/invites/sent", response_model=InvitationListResponse)
def list_sent_invites(
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute","mentor"]))
):
    current_user = get_current_user(token, db)
    return list_sent_invites_to_mentors(db, current_user.id, page, size, status_filter)


@router.get("/invites/received", response_model=InvitationListResponse)
def list_received_invites(
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute","mentor"]))
):
    current_user = get_current_user(token, db)
    return list_received_invites_from_institutes(db, current_user.id, page, size, status_filter)


@router.post("/invite/{invite_id}/respond")
def respond_to_invite(
    invite_id: UUID,
    response: dict,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute","mentor"]))
):
    current_user = get_current_user(token, db)
    result = respond_to_received_invite_as_institute(db, current_user.id, invite_id, response)
    return {"message": "Response processed successfully", "collaboration": result}

# =========================================================
#                 COLLABORATION ROUTES
# =========================================================
# Note: Collaboration management has been simplified.
# Use the unified collaboration API at /collaborations/ instead.
