"""
Application shutdown service module.

This module handles all shutdown operations and cleanup logic.
"""

from services.background_services import BackgroundServiceManager


class ShutdownService:
    """Service for handling application shutdown operations."""
    
    def __init__(self, background_manager: BackgroundServiceManager):
        self.background_manager = background_manager
    
    async def cleanup_application(self):
        """
        Clean up all application services and components.
        
        This method handles the complete shutdown sequence including:
        - Background task cancellation
        - External service connection cleanup
        - Any other cleanup operations
        """
        print("Shutting down EduFair application...")

        try:
            # Stop background services
            await self._stop_background_services()
            
            # Close external connections
            await self._cleanup_external_services()
            
            print("EduFair application shutdown completed")

        except Exception as e:
            print(f"Error during application shutdown: {str(e)}")
    
    async def _stop_background_services(self):
        """Stop all background services and tasks."""
        await self.background_manager.stop_services()
    
    async def _cleanup_external_services(self):
        """Clean up external service connections."""
        # Clear in-memory cache
        from utils.cache import CacheManager
        cache_manager = CacheManager()
        cache_manager.clear_all()
        print("In-memory cache cleared")
