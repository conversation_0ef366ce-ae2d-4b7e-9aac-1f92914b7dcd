"""
Routes for Institute-Mentor Collaborations
Institute perspective for managing collaborations and invitations
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Mentors.CollaborationCrud import (
    create_collaboration, get_collaboration_by_id, update_collaboration,
    delete_collaboration, list_collaborations
)
from Cruds.Institute.Mentor import (
    create_invite_to_mentor, list_received_invites_from_institutes,
    list_sent_invites_to_mentors, respond_to_received_invite_as_institute
)

# Import Schemas
from Schemas.Mentors.MentorInstitutes import (
    MentorInstituteInvite, MentorInstituteInviteOut, InvitationListResponse,
    CollaborationCreate, CollaborationUpdate, CollaborationDetails,
    CollaborationListResponse
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# === INSTITUTE COLLABORATION MANAGEMENT ===

@router.get("/collaborations", response_model=CollaborationListResponse)
def list_institute_collaborations(
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """List all collaborations for the institute"""
    current_user = get_current_user(token, db)
    
    return list_collaborations(
        db=db,
        user_id=current_user.id,
        user_type="institute",
        status_filter=status_filter,
        page=page,
        size=size
    )


@router.get("/collaborations/{collaboration_id}", response_model=CollaborationDetails)
def get_institute_collaboration(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get specific collaboration details"""
    current_user = get_current_user(token, db)
    collaboration = get_collaboration_by_id(db, collaboration_id)
    
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    # Check if institute has access to this collaboration
    if collaboration.institute.id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return collaboration


@router.put("/collaborations/{collaboration_id}", response_model=CollaborationDetails)
def update_institute_collaboration(
    collaboration_id: UUID,
    update_data: CollaborationUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Update collaboration details from institute side"""
    current_user = get_current_user(token, db)
    
    # Check access first
    collaboration = get_collaboration_by_id(db, collaboration_id)
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    if collaboration.institute.id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return update_collaboration(db, collaboration_id, update_data)


@router.post("/collaborations/{collaboration_id}/end")
def end_collaboration(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """End collaboration from institute side"""
    current_user = get_current_user(token, db)
    
    # Check access first
    collaboration = get_collaboration_by_id(db, collaboration_id)
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    if collaboration.institute.id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Update status to ended instead of deleting
    from datetime import datetime, timezone
    update_data = CollaborationUpdate(
        status="ended",
        end_date=datetime.now(timezone.utc)
    )
    
    return update_collaboration(db, collaboration_id, update_data)


# === INSTITUTE INVITATION MANAGEMENT ===

@router.post("/invitations/send-to-mentor", response_model=MentorInstituteInviteOut)
def institute_invite_mentor(
    invitation_data: MentorInstituteInvite,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Institute sends invitation to mentor"""
    current_user = get_current_user(token, db)
    return create_invite_to_mentor(db, current_user.id, invitation_data)


@router.get("/invitations/sent", response_model=InvitationListResponse)
def get_institute_sent_invitations(
    status_filter: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get invitations sent by institute to mentors"""
    current_user = get_current_user(token, db)
    return list_sent_invites_to_mentors(db, current_user.id, page, size, status_filter)


@router.get("/invitations/received", response_model=InvitationListResponse)
def get_institute_received_invitations(
    status_filter: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get invitations received by institute from mentors"""
    current_user = get_current_user(token, db)
    return list_received_invites_from_institutes(db, current_user.id, page, size, status_filter)


@router.post("/invitations/{invitation_id}/respond")
def institute_respond_to_invitation(
    invitation_id: UUID,
    response: dict,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Institute responds to invitation from mentor"""
    current_user = get_current_user(token, db)
    return respond_to_received_invite_as_institute(db, current_user.id, invitation_id, response)


# === MENTOR DISCOVERY FOR INSTITUTES ===

@router.get("/mentors/search")
def search_mentors_for_collaboration(
    subject: Optional[str] = Query(None, description="Filter by subject expertise"),
    min_experience: Optional[int] = Query(None, description="Minimum years of experience"),
    max_hourly_rate: Optional[float] = Query(None, description="Maximum hourly rate"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Search for mentors available for collaboration"""
    current_user = get_current_user(token, db)
    
    # Import mentor search function
    from Cruds.Institute.Mentor import get_mentors
    
    return get_mentors(
        db=db,
        page=page,
        size=size,
        subject_filter=subject,
        min_experience=min_experience,
        max_hourly_rate=max_hourly_rate
    )


@router.get("/mentors/{mentor_id}")
def get_mentor_details_for_institute(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get detailed mentor information for collaboration consideration"""
    current_user = get_current_user(token, db)
    
    from Cruds.Institute.Mentor import get_mentor_with_profile_by_id
    mentor = get_mentor_with_profile_by_id(db, mentor_id)
    
    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    return mentor
