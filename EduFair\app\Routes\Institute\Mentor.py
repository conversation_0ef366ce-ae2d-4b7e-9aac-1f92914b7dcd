from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Institute.Mentor import (
    update_mentor_profile, get_mentor_with_profile_by_id, get_mentors,
    create_invite_to_institute, list_sent_invites_to_institutes,
    delete_invite_sent_to_institute, list_received_invites_from_institutes,
    respond_to_received_invite
)

# Import Schemas
from Schemas.Mentors.Mentor import (
    MentorProfileUpdate,
    MentorUserOut, MentorDetailedOut, MentorListResponse,
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()





@router.put("/profile", response_model=MentorDetailedOut)
def update_my_mentor_profile(
    profile_update: MentorProfileUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Update current mentor's profile"""
    current_user = get_current_user(token, db)
    return update_mentor_profile(db, current_user.id, profile_update)

@router.get("/profile", response_model=MentorDetailedOut)
def get_my_profile(db: Session = Depends(get_db),
                   token: str = Depends(oauth2_scheme),
                   _ = Depends(require_type("mentor"))):
    current_user = get_current_user(token, db)
    return get_mentor_with_profile_by_id(db, current_user.id)



