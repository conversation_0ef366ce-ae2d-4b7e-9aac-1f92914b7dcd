import uuid
import enum
from datetime import datetime, timezone
from sqlalchemy import (
    Column, DateTime, Integer, String, Boolean, Enum, ForeignKey,
    Text, Numeric, UniqueConstraint, Table, CheckConstraint
)
from sqlalchemy.dialects.postgresql import UUI<PERSON>, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship, foreign
from .baseModel import BaseModel


# -----------------------------
# Association Tables
# -----------------------------

teacher_subjects = Table(
    "teacher_subjects",
    BaseModel.metadata,
    Column("teacher_profile_id", UUID(as_uuid=True), ForeignKey("teacher_profiles.id"), primary_key=True),
    Column("subject_id", UUID(as_uuid=True), ForeignKey("subjects.id"), primary_key=True),
)

mentor_expertise_subjects = Table(
    "mentor_expertise_subjects",
    BaseModel.metadata,
    Column("mentor_profile_id", UUID(as_uuid=True), <PERSON><PERSON>ey("mentor_profiles.user_id"), primary_key=True),
    Column("subject_id", UUID(as_uuid=True), ForeignKey("subjects.id"), primary_key=True),
)

mentor_preferred_subjects = Table(
    "mentor_preferred_subjects",
    BaseModel.metadata,
    Column("mentor_profile_id", UUID(as_uuid=True), ForeignKey("mentor_profiles.user_id"), primary_key=True),
    Column("subject_id", UUID(as_uuid=True), ForeignKey("subjects.id"), primary_key=True),
)


# -----------------------------
# Enum
# -----------------------------

class UserTypeEnum(enum.Enum):
    admin = "admin"
    student = "student"
    teacher = "teacher"
    sponsor = "sponsor"
    institute = "institute"
    mentor = "mentor"

class InviteReceivedByEnum(enum.Enum):
    institute = "institute"
    mentor = "mentor"

# -----------------------------
# User and Identity
# -----------------------------

class User(BaseModel):
    __tablename__ = "users"

    username = Column(String, unique=True, nullable=False, index=True)
    email = Column(String, unique=True, nullable=False, index=True)
    mobile = Column(String, unique=True, nullable=False, index=True)
    password_hash = Column(String, nullable=False)
    country = Column(String, nullable=True)
    profile_picture = Column(String, nullable=True)

    user_type = Column(Enum(UserTypeEnum, native_enum=False), nullable=False)
    is_email_verified = Column(Boolean, default=False)
    is_mobile_verified = Column(Boolean, default=False)

    # Profiles
    sponsor_profile = relationship("SponsorProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    institute_profile = relationship("InstituteProfile", back_populates="user", uselist=False,
                                     cascade="all, delete-orphan", foreign_keys="InstituteProfile.user_id")
    mentor_profile = relationship("MentorProfile", back_populates="user", uselist=False,
                                  cascade="all, delete-orphan", foreign_keys="MentorProfile.user_id")
    teacher_profile = relationship("TeacherProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")

    # Identity
    cnic = relationship("CNIC", back_populates="user", uselist=False, cascade="all, delete-orphan")
    passport = relationship("Passport", back_populates="user", uselist=False, cascade="all, delete-orphan")

    # Tasks
    tasks = relationship("TaskStudents", back_populates="student", cascade="all, delete-orphan")
    task_classrooms = relationship("TaskClassroomStudent", back_populates="student", cascade="all, delete-orphan")
    task_attachments = relationship("TaskAttachment", back_populates="student", cascade="all, delete-orphan")
    student_task_attachments = relationship("StudentTaskAttachment", back_populates="student", cascade="all, delete-orphan")

    # Subscription
    subscription = relationship("UserSubscription", back_populates="user", uselist=False)

    # Verification
    verification_tokens = relationship("EmailVerificationToken", back_populates="user", cascade="all, delete-orphan")

    # Mentor invites (as mentor)
    mentor_institute_invites = relationship("MentorInstituteInvite",
                                           foreign_keys="MentorInstituteInvite.mentor_id",
                                           back_populates="mentor",
                                           cascade="all, delete-orphan")


class CNIC(BaseModel):
    __tablename__ = "cnics"

    encrypted_cnic = Column(String, nullable=False)
    cnic_hash = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)

    user = relationship("User", back_populates="cnic")


class Passport(BaseModel):
    __tablename__ = "passports"

    encrypted_passport = Column(String, nullable=False)
    passport_hash = Column(String, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)

    user = relationship("User", back_populates="passport")


# -----------------------------
# Subjects
# -----------------------------

class Subject(BaseModel):
    __tablename__ = "subjects"

    name = Column(String, nullable=False, unique=True)

    tasks = relationship("Task", back_populates="subject", cascade="all, delete-orphan")
    chapters = relationship("Chapter", back_populates="subject", cascade="all, delete-orphan")
    questions = relationship("Question", back_populates="subject")

    teachers = relationship("TeacherProfile", secondary="teacher_subjects", back_populates="subjects")
    mentor_expertise = relationship("MentorProfile", secondary="mentor_expertise_subjects", back_populates="expertise_subjects")
    mentor_preferences = relationship("MentorProfile", secondary="mentor_preferred_subjects", back_populates="preferred_subjects")


# -----------------------------
# Profiles
# -----------------------------

class SponsorProfile(BaseModel):
    __tablename__ = "sponsor_profiles"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    company_name = Column(String, nullable=True)
    logo_url = Column(String, nullable=True)
    description = Column(String, nullable=True)

    user = relationship("User", back_populates="sponsor_profile")


class InstituteProfile(BaseModel):
    __tablename__ = "institute_profiles"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    institute_name = Column(String, nullable=False)
    address = Column(Text, nullable=True)
    accreditation = Column(String, nullable=True)

    description = Column(Text, nullable=True)
    website = Column(String, nullable=True)
    established_year = Column(Integer, nullable=True)
    institute_type = Column(String, nullable=True)

    city = Column(String, nullable=True)
    state = Column(String, nullable=True)
    postal_code = Column(String, nullable=True)

    is_verified = Column(Boolean, default=False)
    verification_status = Column(String, default="pending")
    verification_notes = Column(Text, nullable=True)
    verified_at = Column(DateTime(timezone=True), nullable=True)
    verified_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    submitted_at = Column(DateTime(timezone=True), nullable=True)

    linkedin_url = Column(String, nullable=True)
    facebook_url = Column(String, nullable=True)
    twitter_url = Column(String, nullable=True)

    logo_url = Column(String, nullable=True)
    banner_url = Column(String, nullable=True)

    user = relationship("User", back_populates="institute_profile", foreign_keys=[user_id])
    verifier = relationship("User", foreign_keys=[verified_by])

    mentor_associations = relationship("MentorInstituteAssociation",
                                       primaryjoin="InstituteProfile.user_id == foreign(MentorInstituteAssociation.institute_id)",
                                       cascade="all, delete-orphan")
    competitions = relationship("Event", primaryjoin="InstituteProfile.user_id == foreign(Event.institute_id)")
    documents = relationship("InstituteDocument",
                             primaryjoin="InstituteProfile.user_id == foreign(InstituteDocument.institute_id)",
                             cascade="all, delete-orphan")


class MentorProfile(BaseModel):
    __tablename__ = "mentor_profiles"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)

    profile_image_url = Column(String, nullable=True)
    bio = Column(Text, nullable=True)
    experience_years = Column(Integer, nullable=True)
    hourly_rate = Column(Numeric(10, 2), nullable=True)

    languages = Column(JSON, nullable=True)
    availability_hours = Column(JSON, nullable=True)

    user = relationship("User", back_populates="mentor_profile", foreign_keys=[user_id])

    expertise_subjects = relationship("Subject", secondary="mentor_expertise_subjects", back_populates="mentor_expertise")
    preferred_subjects = relationship("Subject", secondary="mentor_preferred_subjects", back_populates="mentor_preferences")

    institute_associations = relationship("MentorInstituteAssociation",
                                          primaryjoin="MentorProfile.user_id == foreign(MentorInstituteAssociation.mentor_id)",
                                          cascade="all, delete-orphan")
    competition_assignments = relationship("CompetitionMentorAssignment",
                                           primaryjoin="MentorProfile.user_id == foreign(CompetitionMentorAssignment.mentor_id)",
                                           cascade="all, delete-orphan")


class MentorInstituteInvite(BaseModel):
    __tablename__ = "mentor_institute_invites"

    mentor_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    institute_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    receiver_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)  # Who receives the invite
    mentor_email = Column(String, nullable=True)
    invitation_message = Column(Text, nullable=True)
    status = Column(String, default="pending", nullable=True)
    proposed_hourly_rate = Column(Numeric(10, 2), nullable=True)
    proposed_hours_per_week = Column(Integer, nullable=True)
    expertise_areas_needed = Column(JSON, nullable=True)
    contract_terms = Column(Text, nullable=True)
    invited_at = Column(DateTime(timezone=True), default=datetime.now(timezone.utc), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    responded_at = Column(DateTime(timezone=True), nullable=True)
    response_message = Column(Text, nullable=True)
    received_by = Column(Enum(InviteReceivedByEnum, native_enum=False), nullable=True)  # Institute or mentor

    # Legacy fields for backward compatibility
    hourly_rate = Column(Numeric(10, 2), nullable=True)
    hours_per_week = Column(Integer, nullable=True)

    mentor = relationship("User", foreign_keys=[mentor_id], back_populates="mentor_institute_invites")
    institute = relationship("User", foreign_keys=[institute_id])
    receiver = relationship("User", foreign_keys=[receiver_id])


class MentorInstituteAssociation(BaseModel):
    __tablename__ = "mentor_institute_associations"

    mentor_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    institute_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    status = Column(String, default="active", nullable=True)
    hourly_rate = Column(Numeric(10, 2), nullable=True)
    hours_per_week = Column(Integer, nullable=True)
    contract_terms = Column(Text, nullable=True)
    start_date = Column(DateTime(timezone=True), default=datetime.now(timezone.utc), nullable=True)
    end_date = Column(DateTime(timezone=True), nullable=True)
    created_from_invite_id = Column(UUID(as_uuid=True), ForeignKey("mentor_institute_invites.id"), nullable=True)

    mentor = relationship("User", foreign_keys=[mentor_id])
    institute = relationship("User", foreign_keys=[institute_id])
    created_from_invite = relationship("MentorInstituteInvite", foreign_keys=[created_from_invite_id])


class TeacherProfile(BaseModel):
    __tablename__ = "teacher_profiles"

    teacher_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), unique=True, nullable=False)
    bio = Column(Text, nullable=True)
    experience_years = Column(Integer, nullable=True)
    specialization = Column(String, nullable=True)
    website = Column(String, nullable=True)
    office_hours = Column(String, nullable=True)
    rating = Column(Numeric(3, 1), CheckConstraint("rating >= 0 AND rating <= 5"), nullable=True, default=0.0)

    offers_home_tutoring = Column(Boolean, default=False)
    home_address = Column(Text, nullable=True)
    latitude = Column(Numeric(10, 8), nullable=True)
    longitude = Column(Numeric(11, 8), nullable=True)
    tutoring_radius_km = Column(Integer, nullable=True)
    hourly_rate_home = Column(Numeric(10, 2), nullable=True)
    hourly_rate_online = Column(Numeric(10, 2), nullable=True)

    preferred_contact_method = Column(String, nullable=True)
    whatsapp_number = Column(String, nullable=True)

    available_days = Column(JSON, nullable=True)
    available_hours = Column(JSON, nullable=True)

    user = relationship("User", back_populates="teacher_profile")
    subscription = relationship("TeacherSubscription", back_populates="teacher_profile", uselist=False)

    subjects = relationship("Subject", secondary="teacher_subjects", back_populates="teachers")


# -----------------------------
# Subscriptions
# -----------------------------

class SubscriptionPlan(BaseModel):
    __tablename__ = "subscription_plans"

    name = Column(String(255), nullable=False)
    description = Column(Text)
    price = Column(Integer, nullable=False)
    duration_days = Column(Integer, nullable=False)
    features = Column(JSON)
    is_active = Column(Boolean, default=True)

    plan_type = Column(String(50), nullable=False)
    target_user_type = Column(Enum(UserTypeEnum, native_enum=False), nullable=False)

    max_classrooms = Column(Integer, nullable=True)
    max_students_per_classroom = Column(Integer, nullable=True)
    max_exams_per_month = Column(Integer, nullable=True)
    max_questions_per_exam = Column(Integer, nullable=True)
    allows_home_tutoring = Column(Boolean, default=False)
    allows_ai_question_generation = Column(Boolean, default=False)
    allows_advanced_analytics = Column(Boolean, default=False)
    priority_support = Column(Boolean, default=False)

    subscriptions = relationship("UserSubscription", back_populates="plan")


class UserSubscription(BaseModel):
    __tablename__ = "user_subscriptions"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    plan_id = Column(UUID(as_uuid=True), ForeignKey("subscription_plans.id"), nullable=True)

    start_date = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    end_date = Column(DateTime(timezone=True))
    status = Column(String(50), default="active")
    auto_renew = Column(Boolean, default=True)
    payment_reference = Column(String(255))

    is_trial = Column(Boolean, default=False)
    trial_end_date = Column(DateTime(timezone=True), nullable=True)

    billing_cycle = Column(String(20), default="monthly")
    next_billing_date = Column(DateTime(timezone=True), nullable=True)

    current_usage = Column(JSON, nullable=True)

    user = relationship("User", back_populates="subscription")
    plan = relationship("SubscriptionPlan", back_populates="subscriptions")

    __table_args__ = (UniqueConstraint("user_id", name="unique_user_subscription"),)


class TeacherSubscription(BaseModel):
    __tablename__ = "teacher_subscriptions"

    teacher_profile_id = Column(UUID(as_uuid=True), ForeignKey("teacher_profiles.teacher_id"), unique=True, nullable=False)
    plan_id = Column(UUID(as_uuid=True), ForeignKey("plans.id"), nullable=True)

    start_date = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    end_date = Column(DateTime(timezone=True))
    status = Column(String(50), default="active")
    auto_renew = Column(Boolean, default=True)
    payment_reference = Column(String(255))

    is_trial = Column(Boolean, default=False)
    trial_end_date = Column(DateTime(timezone=True), nullable=True)

    billing_cycle = Column(String(20), default="monthly")
    next_billing_date = Column(DateTime(timezone=True), nullable=True)

    current_usage = Column(JSON, nullable=True)

    teacher_profile = relationship("TeacherProfile", back_populates="subscription")
    plan = relationship("Plan", back_populates="subscriptions")

    __table_args__ = (UniqueConstraint("teacher_profile_id", name="unique_teacher_subscription"),)


class Plan(BaseModel):
    __tablename__ = "plans"

    name = Column(String(255), nullable=False)
    description = Column(Text)
    price = Column(Integer, nullable=False)
    duration_days = Column(Integer, nullable=False)
    features = Column(JSON)
    is_active = Column(Boolean, default=True)

    subscriptions = relationship("TeacherSubscription", back_populates="plan")



class TeacherRating(BaseModel):
    __tablename__ = "teacher_ratings"

    teacher_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    student_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    rating = Column(Numeric(3, 1), CheckConstraint("rating >= 0 AND rating <= 5"), nullable=False)

    __table_args__ = (UniqueConstraint("teacher_id", "student_id", name="unique_teacher_student_rating"),)


class InstituteDocument(BaseModel):
    __tablename__ = "institute_documents"

    institute_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    document_type = Column(String, nullable=False)
    document_url = Column(String, nullable=False)
    document_name = Column(String, nullable=False)
    description = Column(Text, nullable=True)

    verified = Column(Boolean, default=False)
    verified_at = Column(DateTime(timezone=True), nullable=True)
    verified_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    institute = relationship("User", foreign_keys=[institute_id])
    verifier = relationship("User", foreign_keys=[verified_by])


class EmailVerificationToken(BaseModel):
    __tablename__ = "email_verification_tokens"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    email = Column(String, nullable=False)
    verification_code = Column(String, nullable=False)
    token_type = Column(String, nullable=False, default="email_verification")

    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_used = Column(Boolean, default=False)
    used_at = Column(DateTime(timezone=True), nullable=True)

    ip_address = Column(String, nullable=True)
    user_agent = Column(Text, nullable=True)

    user = relationship("User", back_populates="verification_tokens")

    def is_expired(self):
        now = datetime.now(timezone.utc)
        return now > self.expires_at

    def is_valid(self):
        return not self.is_used and not self.is_expired()
