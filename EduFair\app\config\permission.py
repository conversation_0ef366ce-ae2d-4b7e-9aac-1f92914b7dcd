from fastapi import Depends, HTTPException, status
from .deps import get_current_user
from sqlalchemy.orm import Session
from .session import get_db
from Models.users import User, UserTypeEnum
from typing import Union, List

def require_type(required_type: Union[str, List[str]]):
    def type_dependency(
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        # Convert single string to list for uniform processing
        if isinstance(required_type, str):
            allowed_types = [required_type, "admin"]
        else:
            allowed_types = list(required_type) + ["admin"]

        if user.user_type.value not in allowed_types:
            # Format the required types for error message
            if isinstance(required_type, str):
                type_display = required_type
            else:
                type_display = " or ".join(required_type)

            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"User '{user.username}' does not have permission for: '{type_display}'",
            )
        return user
    return type_dependency


def require_verified_institute():
    """
    Dependency that ensures the current user is a verified institute.
    Raises HTTPException if user is not an institute or not verified.
    """
    def verification_dependency(
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        # Check if user is institute or admin (admin can bypass verification)
        if user.user_type == UserTypeEnum.admin:
            return user

        if user.user_type != UserTypeEnum.institute:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="This action requires institute account",
            )

        # Check if institute has profile
        if not user.institute_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Institute profile not found. Please complete your profile setup.",
            )

        # Check verification status
        if not user.institute_profile.is_verified:
            verification_status = user.institute_profile.verification_status

            if verification_status == "pending":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Your institute is pending verification. Please wait for admin approval before performing this action.",
                )
            elif verification_status == "rejected":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Your institute verification was rejected. Please contact support for assistance.",
                )
            elif verification_status == "under_review":
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Your institute is currently under review. Please wait for the verification process to complete.",
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Your institute is not verified. Please complete the verification process.",
                )

        return user
    return verification_dependency


def require_institute_with_profile():
    """
    Dependency that ensures the current user is an institute with a complete profile.
    Does not require verification - useful for profile completion endpoints.
    """
    def profile_dependency(
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        # Admin can bypass
        if user.user_type == UserTypeEnum.admin:
            return user

        if user.user_type != UserTypeEnum.institute:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="This action requires institute account",
            )

        # Check if institute has profile
        if not user.institute_profile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Institute profile not found. Please complete your profile setup.",
            )

        return user
    return profile_dependency


def require_verified_institute_or_teacher():
    """
    Dependency for actions that can be performed by verified institutes or teachers.
    """
    def verification_dependency(
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db)
    ):
        # Admin can bypass
        if user.user_type == UserTypeEnum.admin:
            return user

        # Allow teachers (they have their own verification system)
        if user.user_type == UserTypeEnum.teacher:
            return user

        # For institutes, require verification
        if user.user_type == UserTypeEnum.institute:
            if not user.institute_profile:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Institute profile not found. Please complete your profile setup.",
                )

            if not user.institute_profile.is_verified:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Your institute must be verified to perform this action.",
                )
            return user

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="This action requires teacher or verified institute account",
        )
    return verification_dependency
